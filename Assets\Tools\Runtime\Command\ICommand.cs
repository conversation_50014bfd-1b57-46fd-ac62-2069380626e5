namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Represents a command with execute and undo operations.
    /// </summary>
    public interface ICommand
    {
        /// <summary>
        /// Executes the command logic.
        /// </summary>
        void Execute();

        /// <summary>
        /// Reverses the effects of the command.
        /// </summary>
        void Undo();
    }
}
