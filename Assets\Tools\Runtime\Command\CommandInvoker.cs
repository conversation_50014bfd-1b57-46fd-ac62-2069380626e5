using System.Collections.Generic;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Invokes, undoes, redoes, and manages a stack of commands.
    /// </summary>
    public class CommandInvoker
    {
        private readonly Stack<ICommand> undoStack = new Stack<ICommand>();
        private readonly Stack<ICommand> redoStack = new Stack<ICommand>();

        /// <summary>
        /// Gets a value indicating whether there are commands to undo.
        /// </summary>
        public bool CanUndo => undoStack.Count > 0;

        /// <summary>
        /// Gets a value indicating whether there are commands to redo.
        /// </summary>
        public bool CanRedo => redoStack.Count > 0;

        /// <summary>
        /// Executes a command and adds it to the undo stack. Clears the redo stack.
        /// </summary>
        /// <param name="command">The command to execute.</param>
        public void ExecuteCommand(ICommand command)
        {
            command.Execute();
            undoStack.Push(command);
            redoStack.Clear();
        }

        /// <summary>
        /// Undoes the last executed command and adds it to the redo stack.
        /// </summary>
        public void UndoLastCommand()
        {
            if (undoStack.Count > 0)
            {
                var command = undoStack.Pop();
                command.Undo();
                redoStack.Push(command);
            }
        }

        /// <summary>
        /// Redoes the last undone command and adds it back to the undo stack.
        /// </summary>
        public void RedoLastCommand()
        {
            if (redoStack.Count > 0)
            {
                var command = redoStack.Pop();
                command.Execute();
                undoStack.Push(command);
            }
        }

        /// <summary>
        /// Returns the next command to be undone, or null if none.
        /// </summary>
        public ICommand PeekUndo() => undoStack.Count > 0 ? undoStack.Peek() : null;

        /// <summary>
        /// Returns the next command to be redone, or null if none.
        /// </summary>
        public ICommand PeekRedo() => redoStack.Count > 0 ? redoStack.Peek() : null;

        /// <summary>
        /// Clears the undo and redo history.
        /// </summary>
        public void ClearHistory()
        {
            undoStack.Clear();
            redoStack.Clear();
        }
    }
}
