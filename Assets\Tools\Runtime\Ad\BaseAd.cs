using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Base class for all advertisement implementations.
    /// Provides common functionality for ad initialization, loading, and display.
    /// Includes events for tracking ad lifecycle including initialization, loading, errors, and user interactions.
    /// </summary>
    public abstract class BaseAd : MonoBehaviour
    {
        [SerializeField, TextArea]
        protected string adUnitId;

        [SerializeField]
        protected int retryCount = 3;

        [SerializeField]
        protected int totalRetryCount = 9;

        [SerializeField]
        protected float retryCooldown = 1.5f;

        [SerializeField]
        protected float showCooldown = 2f;

        [SerializeField]
        protected UnityEvent onAdImpression = new();

        [SerializeField]
        protected UnityEvent onAdClick = new();

        [SerializeField]
        protected UnityEvent onAdLoad = new();

        [SerializeField]
        protected UnityEvent onAdInitialized = new();

        [SerializeField]
        protected UnityEvent<string> onAdError = new();

        [SerializeField]
        protected UnityEvent onMaxRetryAttemptsReached = new();

        protected bool adInitialized;
        protected bool adLoaded;
        protected int retryCounter = 0;
        protected int totalRetryCounter = 0;
        protected float lastShowTime = 0;

        /// <summary>
        /// Gets whether the ad has been initialized.
        /// </summary>
        public bool AdInitialized => adInitialized;

        /// <summary>
        /// Gets whether the ad has been loaded and is ready to display.
        /// </summary>
        public bool AdLoaded => adLoaded;

        /// <summary>
        /// Gets or sets the ad unit identifier used by the ad network.
        /// </summary>
        public string AdUnitId
        {
            get => adUnitId;
            set => adUnitId = value;
        }

        /// <summary>
        /// Gets the event triggered when an ad impression is recorded.
        /// </summary>
        public UnityEvent OnAdImpression => onAdImpression;

        /// <summary>
        /// Gets the event triggered when the ad is clicked by the user.
        /// </summary>
        public UnityEvent OnAdClick => onAdClick;

        /// <summary>
        /// Gets the event triggered when the ad is successfully loaded.
        /// </summary>
        public UnityEvent OnAdLoad => onAdLoad;

        /// <summary>
        /// Gets the event triggered when the ad is successfully initialized.
        /// </summary>
        public UnityEvent OnAdInitialized => onAdInitialized;

        /// <summary>
        /// Gets the event triggered when an error occurs during ad operations.
        /// </summary>
        public UnityEvent<string> OnAdError => onAdError;

        /// <summary>
        /// Gets the event triggered when maximum retry attempts have been reached.
        /// </summary>
        public UnityEvent OnMaxRetryAttemptsReached => onMaxRetryAttemptsReached;

        /// <summary>
        /// Displays the advertisement if conditions are met.
        /// Must be implemented by derived classes.
        /// </summary>
        public abstract void ShowAd();

        /// <summary>
        /// Loads the advertisement content in advance to reduce loading time when showing the ad.
        /// Must be implemented by derived classes.
        /// When loading is successful, implementations should invoke the onAdLoad event.
        /// </summary>
        public abstract void LoadAd();

        /// <summary>
        /// Initializes the advertisement system. This method is called only once during the lifetime of the application.
        /// Must be implemented by derived classes.
        /// When initialization is successful, implementations should invoke the onAdInitialized event.
        /// </summary>
        public abstract void Initialize();

        /// <summary>
        /// Checks if the ad can be shown based on initialization status, loading status, and waiting period.
        /// </summary>
        /// <returns>True if the ad can be shown, false otherwise.</returns>
        public virtual bool CanShowAd()
        {
            if (!adInitialized)
            {
                string message = $"{GetType().Name}: Ad is not initialized.";
                Debug.LogWarning(message);
                onAdError?.Invoke(message);
                return false;
            }

            if (!adLoaded)
            {
                string message = $"{GetType().Name}: Ad is not loaded.";
                Debug.LogWarning(message);
                onAdError?.Invoke(message);
                return false;
            }

            if (Time.time < lastShowTime)
            {
                string message = $"{GetType().Name}: Ad is on waiting period. Remaining time: {lastShowTime - Time.time:F2} seconds.";
                Debug.LogWarning(message);
                onAdError?.Invoke(message);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Determines if another retry attempt should be made when loading or showing an ad fails.
        /// </summary>
        /// <returns>True if a retry should be attempted, false otherwise.</returns>
        public virtual bool CanRetry()
        {
            if (retryCounter < retryCount && totalRetryCounter < totalRetryCount)
            {
                retryCounter++;
                totalRetryCounter++;
                Debug.Log($"{GetType().Name}: Retry attempt {retryCounter}/{retryCount}. Total retries: {totalRetryCounter}/{totalRetryCount}.");

                return true;
            }

            string message = $"{GetType().Name}: Maximum retry attempts reached.";
            Debug.LogWarning(message);
            onAdError?.Invoke(message);
            onMaxRetryAttemptsReached?.Invoke();
            return false;
        }
    }
}
