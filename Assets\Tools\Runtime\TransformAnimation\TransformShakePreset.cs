using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// ScriptableObject preset for transform shake parameters.
    /// </summary>
    [CreateAssetMenu(fileName = "Transform Shake Preset", menuName = "Scriptable Objects/Transform/Transform Shake Preset")]
    public sealed class TransformShakePreset : ScriptableObject
    {
        /// <summary>
        /// If true, shake duration is infinite.
        /// </summary>
        public bool infiniteDuration = false;
        /// <summary>
        /// Duration of the shake in seconds.
        /// </summary>
        public float duration = 1f;
        /// <summary>
        /// Base intensity of the shake.
        /// </summary>
        public float intensity = 1f;
        /// <summary>
        /// Base frequency of the shake.
        /// </summary>
        public float frequency = 25f;
        /// <summary>
        /// Number of octaves for noise.
        /// </summary>
        public int octaves = 3;
        /// <summary>
        /// Multiplier for intensity per octave.
        /// </summary>
        public float octaveMultiplier = 0.5f;
        /// <summary>
        /// Scale for each octave.
        /// </summary>
        public float octaveScale = 2f;
        /// <summary>
        /// Lerp speed for position.
        /// </summary>
        public float positionLerpSpeed = 10f;
        /// <summary>
        /// Lerp speed for rotation.
        /// </summary>
        public float rotationLerpSpeed = 10f;
        /// <summary>
        /// Curve for intensity over time.
        /// </summary>
        public AnimationCurve intensityCurve = AnimationCurve.EaseInOut(0f, 0f, 1f, 1f);
        /// <summary>
        /// Influence of shake on position.
        /// </summary>
        public Vector3 positionInfluence = Vector3.one;
        /// <summary>
        /// Influence of shake on rotation.
        /// </summary>
        public Vector3 rotationInfluence = Vector3.one;

        [Space(10)]
        [Header("Debug:")]
        /// <summary>
        /// Current duration (runtime).
        /// </summary>
        public float currentDuration = 0;
        /// <summary>
        /// Current intensity (runtime).
        /// </summary>
        public float currentIntensity = 0;
        /// <summary>
        /// Current frequency (runtime).
        /// </summary>
        public float currentFrequency = 0;
        /// <summary>
        /// Current position offset (runtime).
        /// </summary>
        public Vector3 currentPositionOffset;
        /// <summary>
        /// Current rotation offset (runtime).
        /// </summary>
        public Vector3 currentRotationOffset;

        private void OnEnable()
        {
            Reset();
        }

        private void OnDisable()
        {
            Reset();
        }

        /// <summary>
        /// Resets runtime values to initial preset values.
        /// </summary>
        public void Reset()
        {
            currentDuration = duration;
            currentIntensity = intensity;
            currentFrequency = frequency;
            currentPositionOffset = Vector3.zero;
            currentRotationOffset = Vector3.zero;
        }
    }
}