using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Applies extended quality settings to cameras and physics.
    /// </summary>
    public class QualitySettingsExtension : MonoBehaviour
    {
        [SerializeField] private QualitySettingsExtensionData[] availableQualitySettings;

        private void OnEnable()
        {
            QualitySettingsManager.Instance.OnQualityLevelChanged.AddListener(UpdateQualitySettings);
        }

        private void OnDisable()
        {
            QualitySettingsManager.Instance.OnQualityLevelChanged.RemoveListener(UpdateQualitySettings);
        }

        private void UpdateQualitySettings(QualityIndex index)
        {
            UpdateCameraDistance(index);
            UpdatePhysicsSettings(index);
        }

        private void UpdateCameraDistance(QualityIndex qualityIndex)
        {
            if (availableQualitySettings == null) return;
            foreach (var setting in availableQualitySettings)
            {
                if (setting.qualityIndex == qualityIndex)
                {
                    foreach (var cam in Camera.allCameras)
                    {
                        if (cam != null)
                        {
                            cam.farClipPlane = setting.maxCameraDistance;
                        }
                    }
                    Debug.Log($"Camera max distance updated to {setting.maxCameraDistance} for quality {qualityIndex}.");
                    break;
                }
            }
        }

        private void UpdatePhysicsSettings(QualityIndex qualityIndex)
        {
            if (availableQualitySettings == null) return;
            foreach (var setting in availableQualitySettings)
            {
                if (setting.qualityIndex == qualityIndex)
                {
                    Time.fixedDeltaTime = setting.fixedDeltaTime;
                    Physics.defaultSolverIterations = setting.solverIterations;
                    Physics.defaultSolverVelocityIterations = setting.solverVelocityIterations;

                    Debug.Log($"Physics settings updated: fixedDeltaTime {setting.fixedDeltaTime}, " +
                              $"solverIterations {setting.solverIterations}, " +
                              $"solverVelocityIterations {setting.solverVelocityIterations} for quality {qualityIndex}.");
                    break;
                }
            }
        }
    }
}