using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Allows setting offsets for Rigidbody hidden inspector variables such as velocity and force.
    /// Offsets are exposed in the Inspector and applied at runtime.
    /// </summary>
    [RequireComponent(typeof(Rigidbody))]
    public class RigidbodyOffsetController : MonoBehaviour
    {
        [Header("Rigidbody Offsets")]
        [Tooltip("Offset to apply to the Rigidbody's velocity each frame.")]
        [SerializeField]
        private Vector3 velocityOffset;

        [Tooltip("Offset to apply to the Rigidbody's angular velocity each frame.")]
        [SerializeField]
        private Vector3 angularVelocityOffset;

        [Tooltip("Force to apply to the Rigidbody each frame.")]
        [SerializeField]
        private Vector3 forceOffset;

        [Tooltip("Torque to apply to the Rigidbody each frame.")]
        [SerializeField]
        private Vector3 torqueOffset;

        private Rigidbody rb;

        /// <summary>
        /// Gets or sets the velocity offset applied to the Rigidbody.
        /// </summary>
        public Vector3 VelocityOffset
        {
            get => velocityOffset;
            set => velocityOffset = value;
        }

        /// <summary>
        /// Gets or sets the angular velocity offset applied to the Rigidbody.
        /// </summary>
        public Vector3 AngularVelocityOffset
        {
            get => angularVelocityOffset;
            set => angularVelocityOffset = value;
        }

        /// <summary>
        /// Gets or sets the force offset applied to the Rigidbody.
        /// </summary>
        public Vector3 ForceOffset
        {
            get => forceOffset;
            set => forceOffset = value;
        }

        /// <summary>
        /// Gets or sets the torque offset applied to the Rigidbody.
        /// </summary>
        public Vector3 TorqueOffset
        {
            get => torqueOffset;
            set => torqueOffset = value;
        }

        private void Awake()
        {
            rb = GetComponent<Rigidbody>();
        }

        private void FixedUpdate()
        {
            if (velocityOffset != Vector3.zero)
            {
                rb.linearVelocity += velocityOffset;
            }
            if (angularVelocityOffset != Vector3.zero)
            {
                rb.angularVelocity += angularVelocityOffset;
            }
            if (forceOffset != Vector3.zero)
            {
                rb.AddForce(forceOffset, ForceMode.Force);
            }
            if (torqueOffset != Vector3.zero)
            {
                rb.AddTorque(torqueOffset, ForceMode.Force);
            }
        }
    }
}
