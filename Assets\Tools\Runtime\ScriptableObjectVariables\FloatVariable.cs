using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// A ScriptableObject variable that holds a float value.
    /// </summary>
    [CreateAssetMenu(fileName = "FloatVariable", menuName = "Scriptable Objects/Variables/Float Variable")]
    public sealed class FloatVariable : BaseVariable<float>
    {
        /// <summary>
        /// Add two FloatVariables together
        /// </summary>
        public static float operator +(FloatVariable a, FloatVariable b) => a.value + b.value;

        /// <summary>
        /// Subtract one FloatVariable from another
        /// </summary>
        public static float operator -(FloatVariable a, FloatVariable b) => a.value - b.value;

        /// <summary>
        /// Multiply two FloatVariables together
        /// </summary>
        public static float operator *(FloatVariable a, FloatVariable b) => a.value * b.value;

        /// <summary>
        /// Divide one FloatVariable by another
        /// </summary>
        public static float operator /(FloatVariable a, FloatVariable b) => a.value / b.value;

        /// <summary>
        /// Implicitly convert a FloatVariable to a float
        /// </summary>
        public static implicit operator float(FloatVariable variable) => variable.value;
    }
}