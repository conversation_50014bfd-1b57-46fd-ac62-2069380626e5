using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Base class for event channels that don't need to pass any parameters.
    /// </summary>
    public abstract class VoidEventChannelBase : ScriptableObject
    {
        /// <summary>
        /// The event that is raised when Rai<PERSON> is called.
        /// </summary>
        public event UnityAction OnEventRaised;

        /// <summary>
        /// Raises the event.
        /// </summary>
        public void Raise()
        {
            OnEventRaised?.Invoke();
        }
    }

    /// <summary>
    /// Event channel for events that don't need to pass any parameters.
    /// </summary>
    [CreateAssetMenu(fileName = "VoidEventChannel", menuName = "Scriptable Objects/Event Channels/Void Event Channel")]
    public sealed class VoidEventChannel : VoidEventChannelBase
    {
    }
}