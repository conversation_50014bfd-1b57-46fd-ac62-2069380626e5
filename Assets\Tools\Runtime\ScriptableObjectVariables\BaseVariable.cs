using UnityEngine;
using System;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Base class for ScriptableObject variables that can be referenced across the game.
    /// Provides type-safe access to a value of type T with change notification.
    /// </summary>
    /// <typeparam name="T">The type of the variable value</typeparam>
    public abstract class BaseVariable<T> : ScriptableObject
    {
        [SerializeField, Tooltip("The value of this variable")]
        protected T value;

        /// <summary>
        /// Event that is triggered when the value changes
        /// </summary>
        public event Action<T> OnValueChanged;

        /// <summary>
        /// The current value of this variable
        /// </summary>
        public T Value
        {
            get => value;
            set
            {
                this.value = value;
                OnValueChanged?.Invoke(value);
            }
        }
    }
}