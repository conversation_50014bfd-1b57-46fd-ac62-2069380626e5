using UnityEngine;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// Generic Singleton implementation for MonoBehaviours.
    /// Ensures only one instance of the component exists in the scene.
    /// </summary>
    /// <typeparam name="T">The type of the MonoBehaviour that will be a Singleton</typeparam>
    public abstract class Singleton<T> : MonoBehaviour where T : MonoBehaviour
    {
        /// <summary>
        /// The static reference to the instance
        /// </summary>
        protected static T instance;

        /// <summary>
        /// Whether this singleton should persist between scene loads
        /// </summary>
        [Tooltip("If true, this singleton will not be destroyed when loading a new scene")]
        [SerializeField] protected bool persistAcrossScenes = false;

        /// <summary>
        /// Gets the instance of this singleton.
        /// If no instance exists, finds it in the scene.
        /// </summary>
        public static T Instance
        {
            get
            {
                if (instance == null)
                {
                    // Find an existing instance in the scene (faster than FindFirstObjectByType)
                    instance = FindAnyObjectByType<T>();

                    // Log a warning if no instance exists
                    if (instance == null)
                    {
                        Debug.LogWarning($"No instance of {typeof(T).Name} found in scene.");
                    }
                }

                return instance;
            }
        }

        /// <summary>
        /// Called when the script instance is being loaded
        /// </summary>
        protected virtual void Awake()
        {
            if (instance == null)
            {
                // This is the first instance, make it the singleton
                instance = this as T;

                // If configured to persist, mark it to not be destroyed
                if (persistAcrossScenes)
                {
                    DontDestroyOnLoad(gameObject);
                }
            }
            else if (instance != this)
            {
                // Another instance exists, destroy this one
                Debug.LogWarning($"Another instance of {typeof(T).Name} already exists! Destroying this instance.");
                Destroy(gameObject);
            }
        }

        /// <summary>
        /// Called when the MonoBehaviour will be destroyed
        /// </summary>
        protected virtual void OnDestroy()
        {
            // If this is the singleton instance, clear the static reference
            if (instance == this)
            {
                instance = null;
            }
        }
    }
}