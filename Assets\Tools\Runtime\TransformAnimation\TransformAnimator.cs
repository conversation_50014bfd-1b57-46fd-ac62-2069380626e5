using System.Collections.Generic;
using UnityEngine;
using UnityEngine.Events;

namespace SmartVertex.Tools.Runtime
{
    /// <summary>
    /// MonoBehaviour that executes transform animations based on TransformAnimationData
    /// </summary>
    public class TransformAnimator : MonoBehaviour
    {
        /// <summary>
        /// The animation data to use
        /// </summary>
        [Tooltip("The animation data to use")]
        [SerializeField] private TransformAnimatorData animatorData;

        /// <summary>
        /// The transform to animate (defaults to this GameObject's transform)
        /// </summary>
        [Tooltip("The transform to animate (defaults to this GameObject's transform)")]
        [SerializeField] private Transform targetTransform;

        /// <summary>
        /// Event triggered when the animation starts
        /// </summary>
        [Tooltip("Event triggered when the animation starts")]
        [SerializeField] private UnityEvent animationStartEvent;

        /// <summary>
        /// Event triggered when the animation completes
        /// </summary>
        [Tooltip("Event triggered when the animation completes")]
        [SerializeField] private UnityEvent animationCompleteEvent;

        /// <summary>
        /// Event triggered when a step in the animation completes
        /// </summary>
        [Tooltip("Event triggered when a step in the animation completes")]
        [SerializeField] private UnityEvent<int> stepCompleteEvent;

        private bool isPlaying = false;
        private int currentStepIndex = 0;
        private float currentStepTime = 0f;

        /// <summary>
        /// Gets or sets the animation data
        /// </summary>
        public TransformAnimatorData AnimationData
        {
            get => animatorData;
            set => animatorData = value;
        }

        /// <summary>
        /// Gets or sets the target transform
        /// </summary>
        public Transform TargetTransform
        {
            get => targetTransform;
            set => targetTransform = value;
        }

        /// <summary>
        /// Gets or sets the event triggered when the animation starts
        /// </summary>
        public UnityEvent OnAnimationStart
        {
            get => animationStartEvent;
            set => animationStartEvent = value;
        }

        /// <summary>
        /// Gets or sets the event triggered when the animation completes
        /// </summary>
        public UnityEvent OnAnimationComplete
        {
            get => animationCompleteEvent;
            set => animationCompleteEvent = value;
        }

        /// <summary>
        /// Gets or sets the event triggered when a step in the animation completes
        /// </summary>
        public UnityEvent<int> OnStepComplete
        {
            get => stepCompleteEvent;
            set => stepCompleteEvent = value;
        }

        /// <summary>
        /// Gets whether the animation is currently playing
        /// </summary>
        public bool IsPlaying => isPlaying;

        /// <summary>
        /// Gets the current step index
        /// </summary>
        public int CurrentStepIndex => currentStepIndex;

        private void Awake()
        {
            // If no target transform is specified, use this GameObject's transform
            if (targetTransform == null)
            {
                targetTransform = transform;
            }
        }

        private void OnEnable()
        {
            if (animatorData != null && animatorData.playOnEnable)
            {
                Play();
            }
        }

        private void Update()
        {
            if (isPlaying && animatorData != null && animatorData.updateMode == TransformUpdateMode.Update)
            {
                UpdateAnimation(Time.deltaTime);
            }
        }

        private void FixedUpdate()
        {
            if (isPlaying && animatorData != null && animatorData.updateMode == TransformUpdateMode.FixedUpdate)
            {
                UpdateAnimation(Time.fixedDeltaTime);
            }
        }

        private void LateUpdate()
        {
            if (isPlaying && animatorData != null && animatorData.updateMode == TransformUpdateMode.LateUpdate)
            {
                UpdateAnimation(Time.deltaTime);
            }
        }

        /// <summary>
        /// Plays the animation from the beginning
        /// </summary>
        public void Play()
        {
            if (animatorData == null || animatorData.animationSteps == null || animatorData.animationSteps.Length == 0)
            {
                Debug.LogWarning("Cannot play animation: Animation data is null or has no steps.");
                return;
            }

            Stop();
            isPlaying = true;
            currentStepIndex = 0;
            currentStepTime = 0f;
            animationStartEvent?.Invoke();
        }

        /// <summary>
        /// Pauses the animation
        /// </summary>
        public void Pause()
        {
            isPlaying = false;
        }

        /// <summary>
        /// Resumes the animation from where it was paused
        /// </summary>
        public void Resume()
        {
            if (animatorData == null)
            {
                Debug.LogWarning("Cannot resume animation: Animation data is null.");
                return;
            }

            isPlaying = true;
        }

        /// <summary>
        /// Stops the animation and resets its state
        /// </summary>
        public void Stop()
        {
            isPlaying = false;
            currentStepIndex = 0;
            currentStepTime = 0f;
            initialValues.Clear();
        }

        private void UpdateAnimation(float deltaTime)
        {
            if (animatorData == null || animatorData.animationSteps == null ||
                currentStepIndex >= animatorData.animationSteps.Length || !isPlaying)
            {
                return;
            }

            TransformAnimationStep currentStep = animatorData.animationSteps[currentStepIndex];

            // If this is the first frame of this step, capture initial values
            if (currentStepTime == 0f)
            {
                CaptureInitialValues(currentStep);
            }

            currentStepTime += deltaTime;

            // Calculate normalized time (0 to 1) and apply animation curve
            float normalizedTime = Mathf.Clamp01(currentStepTime / currentStep.duration);
            float curveValue = currentStep.animationCurve.Evaluate(normalizedTime);

            // Apply all animation modes in the current step
            ApplyAnimationStep(currentStep, curveValue);

            // Check if the current step is complete
            if (normalizedTime >= 1f)
            {
                stepCompleteEvent?.Invoke(currentStepIndex);
                currentStepIndex++;
                currentStepTime = 0f;

                // Check if the entire animation is complete
                if (currentStepIndex >= animatorData.animationSteps.Length)
                {
                    if (animatorData.loop)
                    {
                        // Loop back to the beginning
                        currentStepIndex = 0;
                    }
                    else
                    {
                        // Animation is complete
                        isPlaying = false;
                        animationCompleteEvent?.Invoke();
                    }
                }
            }
        }

        private void ApplyAnimationStep(TransformAnimationStep step, float t)
        {
            if (step.animationModes == null || targetTransform == null)
            {
                return;
            }

            foreach (TransformAnimationMode mode in step.animationModes)
            {
                ApplyAnimationMode(mode, t);
            }
        }

        // Store initial values for each animation mode
        private readonly Dictionary<TransformAnimationMode, object> initialValues = new();

        private void CaptureInitialValues(TransformAnimationStep step)
        {
            initialValues.Clear();

            if (step.animationModes == null || targetTransform == null)
            {
                return;
            }

            foreach (TransformAnimationMode mode in step.animationModes)
            {
                // Capture current transform value
                switch (mode.property)
                {
                    case TransformProperty.Position:
                        Vector3 posValue = mode.useLocalSpace ? targetTransform.localPosition : targetTransform.position;
                        initialValues[mode] = posValue;
                        break;
                    case TransformProperty.Rotation:
                        Quaternion rotValue = mode.useLocalSpace ? targetTransform.localRotation : targetTransform.rotation;
                        initialValues[mode] = rotValue;
                        break;
                    case TransformProperty.Scale:
                        Vector3 scaleValue = targetTransform.localScale;
                        initialValues[mode] = scaleValue;
                        break;
                }
            }
        }

        private void ApplyAnimationMode(TransformAnimationMode mode, float t)
        {
            // Apply the animation based on property type
            switch (mode.property)
            {
                case TransformProperty.Position:
                    ApplyPositionAnimation(mode, t);
                    break;

                case TransformProperty.Rotation:
                    ApplyRotationAnimation(mode, t);
                    break;

                case TransformProperty.Scale:
                    ApplyScaleAnimation(mode, t);
                    break;
            }
        }

        private void ApplyPositionAnimation(TransformAnimationMode mode, float t)
        {
            // Get the initial position value
            Vector3 startValue;
            if (initialValues.TryGetValue(mode, out object initialValue) && initialValue is Vector3 initialVector)
            {
                startValue = initialVector;
            }
            else
            {
                // If not found, use current value
                startValue = mode.useLocalSpace ? targetTransform.localPosition : targetTransform.position;
                initialValues[mode] = startValue;
            }

            // Calculate the target value based on application mode
            Vector3 finalTargetValue;
            if (mode.applicationMode == ValueApplicationMode.Incremental)
            {
                // For incremental mode, add the target value to the initial value
                finalTargetValue = startValue + mode.targetValue;
            }
            else
            {
                // For absolute mode, use the target value directly
                finalTargetValue = mode.targetValue;
            }

            // Apply the position animation
            if (mode.useLocalSpace)
            {
                targetTransform.localPosition = Vector3.Lerp(startValue, finalTargetValue, t);
            }
            else
            {
                targetTransform.position = Vector3.Lerp(startValue, finalTargetValue, t);
            }
        }

        private void ApplyRotationAnimation(TransformAnimationMode mode, float t)
        {
            // Get the initial rotation value
            Quaternion startRotation;
            if (initialValues.TryGetValue(mode, out object initialValue) && initialValue is Quaternion initialQuaternion)
            {
                startRotation = initialQuaternion;
            }
            else
            {
                // If not found, use current value
                startRotation = mode.useLocalSpace ? targetTransform.localRotation : targetTransform.rotation;
                initialValues[mode] = startRotation;
            }

            // Calculate the target rotation based on application mode
            Quaternion finalTargetRotation;
            if (mode.applicationMode == ValueApplicationMode.Incremental)
            {
                // For incremental mode, add the Euler angles to the initial rotation
                Quaternion additionalRotation = Quaternion.Euler(mode.targetValue);
                finalTargetRotation = startRotation * additionalRotation;
            }
            else
            {
                // For absolute mode, use the target Euler angles directly
                finalTargetRotation = Quaternion.Euler(mode.targetValue);
            }

            // Apply the rotation animation using Quaternion.Slerp to avoid gimbal lock
            // Quaternion.Slerp provides smooth spherical interpolation between rotations,
            // which is more natural and avoids the issues that can occur with Euler angle interpolation
            if (mode.useLocalSpace)
            {
                targetTransform.localRotation = Quaternion.Slerp(startRotation, finalTargetRotation, t);
            }
            else
            {
                targetTransform.rotation = Quaternion.Slerp(startRotation, finalTargetRotation, t);
            }
        }

        private void ApplyScaleAnimation(TransformAnimationMode mode, float t)
        {
            // Get the initial scale value
            Vector3 startValue;
            if (initialValues.TryGetValue(mode, out object initialValue) && initialValue is Vector3 initialVector)
            {
                startValue = initialVector;
            }
            else
            {
                // If not found, use current value
                startValue = targetTransform.localScale;
                initialValues[mode] = startValue;
            }

            // Calculate the target value based on application mode
            Vector3 finalTargetValue;
            if (mode.applicationMode == ValueApplicationMode.Incremental)
            {
                // For incremental mode, add the target value to the initial value
                finalTargetValue = startValue + mode.targetValue;
            }
            else
            {
                // For absolute mode, use the target value directly
                finalTargetValue = mode.targetValue;
            }

            // Scale is always local
            targetTransform.localScale = Vector3.Lerp(startValue, finalTargetValue, t);
        }

        /// <summary>
        /// Plays a specific animation step
        /// </summary>
        /// <param name="stepIndex">The index of the step to play</param>
        public void PlayStep(int stepIndex)
        {
            if (animatorData == null || animatorData.animationSteps == null ||
                stepIndex < 0 || stepIndex >= animatorData.animationSteps.Length)
            {
                Debug.LogWarning($"Cannot play step {stepIndex}: Invalid step index.");
                return;
            }

            initialValues.Clear();
            currentStepIndex = stepIndex;
            currentStepTime = 0f;
            isPlaying = true;

            // Capture initial values for this step
            CaptureInitialValues(animatorData.animationSteps[stepIndex]);
        }
    }
}
